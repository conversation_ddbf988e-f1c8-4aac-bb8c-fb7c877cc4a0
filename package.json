{"name": "vue-pure-admin", "version": "6.0.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build && generate-version-file", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/vue-pure-admin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/vue-pure-admin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@howdyjs/mouse-menu": "^2.1.7", "@infectoone/vue-ganttastic": "^2.3.2", "@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.28", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.1", "@pureadmin/utils": "^2.6.2", "@vue-flow/background": "^1.3.2", "@vue-flow/core": "^1.45.0", "@vueuse/core": "^13.5.0", "@vueuse/motion": "^3.0.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.11.0", "china-area-data": "^5.0.1", "codemirror": "^5.65.19", "codemirror-editor-vue3": "^2.8.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "deep-chat": "^2.2.2", "echarts": "^5.6.0", "el-table-infinite-scroll": "^3.0.6", "element-plus": "^2.10.4", "highlight.js": "^11.11.1", "intro.js": "^7.2.0", "js-cookie": "^3.0.5", "jsbarcode": "^3.12.1", "localforage": "^1.10.0", "mint-filter": "^4.0.3", "mitt": "^3.0.1", "mqtt": "4.3.7", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^3.0.3", "pinyin-pro": "^3.26.0", "plus-pro-components": "^0.1.26", "qrcode": "^1.5.4", "qs": "^6.14.0", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.6", "swiper": "^11.2.10", "typeit": "^8.8.7", "v-contextmenu": "^3.2.0", "v3-infinite-loading": "^1.3.2", "vditor": "^3.11.1", "version-rocket": "^1.7.4", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-json-pretty": "^2.5.0", "vue-pdf-embed": "^2.1.3", "vue-router": "^4.5.1", "vue-tippy": "^6.7.1", "vue-types": "^6.0.0", "vue-virtual-scroller": "2.0.0-beta.8", "vue-waterfall-plugin-next": "^2.6.7", "vue3-danmaku": "^1.6.6", "vue3-puzzle-vcode": "^1.1.7", "vuedraggable": "^4.1.0", "vxe-table": "4.6.25", "wavesurfer.js": "^7.10.1", "xgplayer": "^3.0.22", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@eslint/js": "^9.31.0", "@faker-js/faker": "^9.9.0", "@iconify/json": "^2.2.362", "@iconify/vue": "4.2.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@tailwindcss/vite": "^4.1.11", "@types/codemirror": "^5.60.16", "@types/dagre": "^0.7.53", "@types/intro.js": "^5.1.5", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.9", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "boxen": "^8.0.1", "code-inspector-plugin": "^0.20.17", "cssnano": "^7.1.0", "dagre": "^0.8.5", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "gradient-string": "^3.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-load-config": "^6.0.1", "postcss-scss": "^4.0.9", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.22.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-prettier": "^5.0.3", "svgo": "^4.0.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "unplugin-icons": "^22.1.0", "vite": "^7.0.5", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.3"}, "engines": {"node": "^20.19.0 || >=22.12.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "lodash.isequal": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "onlyBuiltDependencies": ["@parcel/watcher", "core-js", "es5-ext", "esbuild", "typeit", "vue-demi"], "ignoredBuiltDependencies": ["@tailwindcss/oxide", "vue3-dan<PERSON><PERSON>"]}}